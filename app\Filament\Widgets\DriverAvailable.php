<?php

namespace App\Filament\Widgets;

use App\Models\Driver;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget;
use Filament\Actions\BulkActionGroup;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;

class DriverAvailable extends TableWidget
{
    public function table(Table $table): Table
    {
        return $table
            ->heading('Driver Availability')
            ->description('List of available drivers')
            ->query(fn(): Builder => Driver::query())
            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('name'),
                TextColumn::make('company.name'),
            ])
            ->filters([
                //
            ]);
    }
}
