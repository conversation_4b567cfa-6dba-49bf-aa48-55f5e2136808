<?php

namespace App\Filament\Widgets;

use Filament\Actions\BulkActionGroup;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Vehicle;

class VehicleAvailable extends TableWidget
{
    public function table(Table $table): Table
    {
        return $table
            ->heading('Vehicle Availability')
            ->description('List of available vehicles')
            ->query(fn(): Builder => Vehicle::query())
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->headerActions([
                //
            ])
            ->recordActions([
                //
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    //
                ]),
            ]);
    }
}
