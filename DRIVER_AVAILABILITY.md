# Driver Availability Feature

## Overview

The Driver Availability feature allows you to view available drivers for a selected time period. This is useful for scheduling trips and ensuring you have the right drivers available when needed.

## Features

### 🎯 **Period-Based Filtering**
- Select a specific date and time range to check driver availability
- Default period is set to current time + 8 hours for convenience
- Real-time validation ensures "Available Until" is after "Available From"

### 📊 **Real-Time Status**
- **Available** (Green Badge): Driver has no overlapping trips in the selected period
- **Busy** (Red Badge): Driver has active trips that conflict with the selected period
- Auto-refresh every 30 seconds to show current status

### 🔍 **Advanced Search & Sorting**
- Search by driver name or license number
- Sort by name, company, or other columns
- Row indexing for easy reference

### 📱 **Responsive Design**
- Works seamlessly on desktop and mobile devices
- Collapsible filters and intuitive date/time pickers

## How to Use

### 1. **Access the Widget**
The Driver Availability widget is displayed on the main dashboard at `/app/dashboard`.

### 2. **View All Drivers**
By default, the widget shows all drivers with their current availability status.

### 3. **Filter by Time Period**
1. Click the **Filter** button (funnel icon) in the toolbar
2. Set your desired **Available From** date and time
3. Set your desired **Available Until** date and time
4. Click **Apply filters**

### 4. **Interpret Results**
- Only drivers available during the entire selected period will be shown
- The filter indicator shows your selected time range
- Status badges indicate current availability

### 5. **Clear Filters**
- Click the **X** button next to the filter indicator, or
- Click **Reset** in the filter dropdown

## Technical Implementation

### Database Query Optimization
The feature uses the existing `availableBetween` scope on the Driver model:

```php
// Finds drivers with no overlapping trips in the specified period
Driver::availableBetween($startTime, $endTime)
```

### Real-Time Updates
- Widget polls every 30 seconds for live status updates
- Efficient caching prevents performance issues
- Eager loading of relationships (company, trips) for optimal performance

### Filter Persistence
- Applied filters are maintained during navigation
- Clear visual indicators show active filter state
- Easy removal of individual or all filters

## Use Cases

### 📅 **Trip Planning**
Check which drivers are available for a specific trip time slot before creating the trip.

### 🚛 **Resource Management**
Get an overview of driver availability for the day/week to optimize scheduling.

### 📞 **Customer Service**
Quickly check driver availability when customers call to book trips.

### 📈 **Capacity Planning**
Analyze driver availability patterns to identify peak times and staffing needs.

## Integration with Trip Scheduling

The availability data integrates seamlessly with the existing trip scheduling system:

- Uses the same `overlapping` scope logic as trip validation
- Respects company boundaries (drivers only show for their assigned company)
- Considers all trip statuses (scheduled, active, completed, cancelled)

## Performance Considerations

- **Caching**: Status calculations are optimized with appropriate caching
- **Indexing**: Database indexes on driver_id, starts_at, and ends_at ensure fast queries
- **Pagination**: Large driver lists are paginated for better performance
- **Lazy Loading**: Related data is loaded efficiently to minimize database queries

## Testing

The feature includes comprehensive tests covering:
- Widget rendering and basic functionality
- Filter application and period selection
- Status calculation accuracy
- Filter indicator display
- Edge cases and error handling

Run tests with:
```bash
./vendor/bin/phpunit tests/Feature/DriverAvailabilityWidgetTest.php
```

## Future Enhancements

Potential improvements for future versions:
- Export availability reports to PDF/Excel
- Email notifications for availability changes
- Integration with external calendar systems
- Bulk availability updates
- Historical availability analytics
